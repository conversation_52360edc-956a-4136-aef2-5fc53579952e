#!/bin/bash

echo "🚀 创建完整的iOS项目..."

# 使用xcodegen创建项目（如果没有安装，我们提供手动步骤）
if command -v xcodegen &> /dev/null; then
    echo "✅ 使用xcodegen创建项目..."
    
    # 创建project.yml配置文件
    cat > project.yml << 'EOF'
name: WeChatMultiAccount
options:
  bundleIdPrefix: com.yourcompany
  deploymentTarget:
    iOS: "17.6"
  developmentLanguage: zh-Hans

targets:
  WeChatMultiAccount:
    type: application
    platform: iOS
    sources:
      - path: WeChatMultiAccount
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.yourcompany.WeChatMultiAccount
      MARKETING_VERSION: "1.0"
      CURRENT_PROJECT_VERSION: "1"
      DEVELOPMENT_TEAM: ""
      CODE_SIGN_STYLE: Automatic
      INFOPLIST_FILE: WeChatMultiAccount/Info.plist
    dependencies: []

schemes:
  WeChatMultiAccount:
    build:
      targets:
        WeChatMultiAccount: all
    run:
      config: Debug
    archive:
      config: Release
EOF

    # 生成Xcode项目
    xcodegen generate
    
    if [ $? -eq 0 ]; then
        echo "✅ Xcode项目生成成功！"
    else
        echo "❌ xcodegen生成失败，请手动创建项目"
    fi
else
    echo "⚠️ 未安装xcodegen，请手动在Xcode中创建项目"
    echo ""
    echo "📱 手动创建步骤："
    echo "1. 打开Xcode"
    echo "2. File -> New -> Project"
    echo "3. 选择 iOS -> App"
    echo "4. 项目信息："
    echo "   - Product Name: WeChatMultiAccount"
    echo "   - Bundle Identifier: com.yourcompany.WeChatMultiAccount"
    echo "   - Language: Swift"
    echo "   - Interface: UIKit"
    echo "   - Use Core Data: 不勾选"
    echo "5. 保存到当前目录: $(pwd)"
    echo ""
    echo "项目创建完成后，运行以下命令添加源代码："
    echo "bash add_source_files.sh"
fi

echo ""
echo "🎯 接下来的步骤："
echo "1. 在Xcode中打开项目"
echo "2. 添加源代码文件"
echo "3. 配置开发者账号"
echo "4. 运行测试"
