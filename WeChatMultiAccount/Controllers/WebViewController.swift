import UIKit
import WebKit

class WebViewController: UIViewController {

    // MARK: - UI Components
    private var webView: WKWebView!
    private let progressView = UIProgressView(progressViewStyle: .default)
    private let refreshButton = UIBarButtonItem()
    private let backButton = UIBarButtonItem()
    private let forwardButton = UIBarButtonItem()
    private let closeButton = UIBarButtonItem()
    private let shareButton = UIBarButtonItem()

    // MARK: - Properties
    private let account: WeChatAccount
    private let accountManager = AccountManager.shared
    private var progressObserver: NSKeyValueObservation?
    private var isLoading = false

    // MARK: - Constants
    private let weChatWebURL = "https://wx.qq.com/"

    // MARK: - Initialization
    init(account: WeChatAccount) {
        self.account = account
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupWebView()
        setupUI()
        setupConstraints()
        setupObservers()
        loadWeChat()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        if isBeingDismissed {
            saveCookies()
        }
    }

    deinit {
        progressObserver?.invalidate()
        print("🗑 WebViewController 已释放")
    }

    // MARK: - Setup Methods
    private func setupWebView() {
        let configuration = WKWebViewConfiguration()

        // 设置用户代理 - 模拟桌面浏览器
        configuration.applicationNameForUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

        // 允许JavaScript
        configuration.preferences.javaScriptEnabled = true
        configuration.preferences.javaScriptCanOpenWindowsAutomatically = true

        // 允许媒体播放
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []

        // 创建WebView
        webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = self
        webView.uiDelegate = self
        webView.allowsBackForwardNavigationGestures = true

        // 恢复Cookie
        restoreCookies()
    }

    private func setupUI() {
        title = account.displayName
        view.backgroundColor = .systemBackground

        // 设置导航栏按钮
        closeButton.image = UIImage(systemName: "xmark.circle.fill")
        closeButton.target = self
        closeButton.action = #selector(closeTapped)

        refreshButton.image = UIImage(systemName: "arrow.clockwise")
        refreshButton.target = self
        refreshButton.action = #selector(refreshTapped)

        backButton.image = UIImage(systemName: "chevron.left")
        backButton.target = self
        backButton.action = #selector(backTapped)

        forwardButton.image = UIImage(systemName: "chevron.right")
        forwardButton.target = self
        forwardButton.action = #selector(forwardTapped)

        shareButton.image = UIImage(systemName: "square.and.arrow.up")
        shareButton.target = self
        shareButton.action = #selector(shareTapped)

        navigationItem.leftBarButtonItem = closeButton
        navigationItem.rightBarButtonItems = [shareButton, refreshButton, forwardButton, backButton]

        // 设置进度条
        progressView.progressTintColor = .systemBlue
        progressView.trackTintColor = .systemGray5
        progressView.alpha = 0

        view.addSubview(progressView)
        view.addSubview(webView)

        updateNavigationButtons()
    }

    private func setupConstraints() {
        progressView.translatesAutoresizingMaskIntoConstraints = false
        webView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            progressView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            progressView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            progressView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            progressView.heightAnchor.constraint(equalToConstant: 3),

            webView.topAnchor.constraint(equalTo: progressView.bottomAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    private func setupObservers() {
        // 监听加载进度
        progressObserver = webView.observe(\.estimatedProgress, options: .new) { [weak self] _, _ in
            DispatchQueue.main.async {
                self?.updateProgress()
            }
        }
    }

    // MARK: - Actions
    @objc private func closeTapped() {
        saveCookies()
        dismiss(animated: true)
    }

    @objc private func refreshTapped() {
        webView.reload()
    }

    @objc private func backTapped() {
        if webView.canGoBack {
            webView.goBack()
        }
    }

    @objc private func forwardTapped() {
        if webView.canGoForward {
            webView.goForward()
        }
    }

    @objc private func shareTapped() {
        guard let url = webView.url else { return }

        let activityViewController = UIActivityViewController(
            activityItems: [url],
            applicationActivities: nil
        )

        // iPad支持
        if let popover = activityViewController.popoverPresentationController {
            popover.barButtonItem = shareButton
        }

        present(activityViewController, animated: true)
    }

    // MARK: - Private Methods
    private func loadWeChat() {
        guard let url = URL(string: weChatWebURL) else {
            showAlert(title: "错误", message: "无效的微信网址")
            return
        }

        var request = URLRequest(url: url)
        request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
        request.timeoutInterval = 30

        webView.load(request)
        print("🌐 开始加载微信网页版: \(weChatWebURL)")
    }

    private func updateProgress() {
        let progress = Float(webView.estimatedProgress)
        progressView.setProgress(progress, animated: true)

        // 显示/隐藏进度条
        if progress >= 1.0 {
            isLoading = false
            UIView.animate(withDuration: 0.3, delay: 0.3, options: .curveEaseOut) {
                self.progressView.alpha = 0
            }
        } else {
            isLoading = true
            progressView.alpha = 1
        }

        // 更新刷新按钮状态
        refreshButton.isEnabled = !isLoading
    }

    private func updateNavigationButtons() {
        backButton.isEnabled = webView.canGoBack
        forwardButton.isEnabled = webView.canGoForward
    }

    private func saveCookies() {
        let cookieStore = webView.configuration.websiteDataStore.httpCookieStore

        cookieStore.getAllCookies { [weak self] cookies in
            guard let self = self else { return }

            // 过滤微信相关的Cookie
            let weChatCookies = cookies.filter { cookie in
                let domain = cookie.domain.lowercased()
                return domain.contains("qq.com") ||
                       domain.contains("wechat.com") ||
                       domain.contains("weixin.qq.com")
            }

            if !weChatCookies.isEmpty {
                self.accountManager.saveCookies(for: self.account.id, cookies: weChatCookies)
                print("🍪 Cookie保存成功，数量: \(weChatCookies.count)")
            }
        }
    }

    private func restoreCookies() {
        guard let cookies = accountManager.getCookies(for: account.id) else {
            print("🍪 未找到已保存的Cookie")
            return
        }

        let cookieStore = webView.configuration.websiteDataStore.httpCookieStore

        for cookie in cookies {
            cookieStore.setCookie(cookie) { [weak self] in
                // Cookie设置完成的回调
            }
        }

        print("🍪 Cookie恢复成功，数量: \(cookies.count)")
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showNetworkError() {
        let alert = UIAlertController(
            title: "网络连接失败",
            message: "无法连接到微信服务器，请检查网络连接后重试。",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "重试", style: .default) { [weak self] _ in
            self?.loadWeChat()
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { [weak self] _ in
            self?.dismiss(animated: true)
        })

        present(alert, animated: true)
    }
}

// MARK: - WKNavigationDelegate
extension WebViewController: WKNavigationDelegate {

    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        updateNavigationButtons()
        print("🌐 开始加载页面")
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        updateNavigationButtons()

        // 注入自定义样式和脚本
        injectCustomStyles()

        print("✅ 页面加载完成: \(webView.url?.absoluteString ?? "未知")")
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("❌ 页面加载失败: \(error.localizedDescription)")
        showNetworkError()
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        print("❌ 页面预加载失败: \(error.localizedDescription)")
        showNetworkError()
    }

    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {

        guard let url = navigationAction.request.url else {
            decisionHandler(.allow)
            return
        }

        let urlString = url.absoluteString.lowercased()

        // 允许微信相关域名
        let allowedDomains = [
            "qq.com",
            "wechat.com",
            "weixin.qq.com",
            "wx.qq.com"
        ]

        let isAllowed = allowedDomains.contains { domain in
            url.host?.lowercased().contains(domain) == true
        }

        if isAllowed {
            decisionHandler(.allow)
            print("🔗 允许导航到: \(url.absoluteString)")
        } else {
            // 对于外部链接，在Safari中打开
            if url.scheme == "https" || url.scheme == "http" {
                UIApplication.shared.open(url)
                print("🔗 在Safari中打开外部链接: \(url.absoluteString)")
            }
            decisionHandler(.cancel)
        }
    }

    private func injectCustomStyles() {
        // 注入自定义CSS样式，优化移动端显示
        let cssString = """
            /* 优化移动端显示 */
            body {
                -webkit-user-select: none;
                -webkit-touch-callout: none;
                font-size: 14px !important;
            }

            /* 隐藏不必要的元素 */
            .download_entry,
            .web_wechat_login_switch,
            .web_wechat_screenshot {
                display: none !important;
            }

            /* 优化聊天界面 */
            .chat_bd {
                padding: 8px !important;
            }

            /* 优化输入框 */
            .edit_area {
                font-size: 16px !important;
                line-height: 1.4 !important;
            }

            /* 优化按钮 */
            .btn {
                padding: 8px 16px !important;
                border-radius: 6px !important;
            }

            /* 响应式布局 */
            @media (max-width: 768px) {
                .panel {
                    width: 100% !important;
                }

                .chat_list {
                    width: 100% !important;
                }
            }
        """

        let script = """
            var style = document.createElement('style');
            style.innerHTML = '\(cssString.replacingOccurrences(of: "\n", with: " "))';
            document.head.appendChild(style);

            // 优化触摸体验
            document.addEventListener('touchstart', function() {}, {passive: true});

            // 自动调整视口
            var viewport = document.querySelector('meta[name=viewport]');
            if (!viewport) {
                viewport = document.createElement('meta');
                viewport.name = 'viewport';
                document.head.appendChild(viewport);
            }
            viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';

            console.log('✅ 自定义样式注入完成');
        """

        webView.evaluateJavaScript(script) { result, error in
            if let error = error {
                print("❌ 脚本注入失败: \(error)")
            } else {
                print("✅ 自定义样式注入成功")
            }
        }
    }
}

// MARK: - WKUIDelegate
extension WebViewController: WKUIDelegate {

    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {

        // 在当前WebView中打开新窗口请求
        if let url = navigationAction.request.url {
            webView.load(URLRequest(url: url))
            print("🔗 在当前窗口中打开: \(url.absoluteString)")
        }

        return nil
    }

    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {

        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completionHandler()
        })

        present(alert, animated: true)
    }

    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {

        let alert = UIAlertController(title: "确认", message: message, preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completionHandler(true)
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            completionHandler(false)
        })

        present(alert, animated: true)
    }

    func webView(_ webView: WKWebView, runJavaScriptTextInputPanelWithPrompt prompt: String, defaultText: String?, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (String?) -> Void) {

        let alert = UIAlertController(title: "输入", message: prompt, preferredStyle: .alert)

        alert.addTextField { textField in
            textField.text = defaultText
            textField.placeholder = "请输入内容"
        }

        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            let text = alert.textFields?.first?.text
            completionHandler(text)
        })

        alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
            completionHandler(nil)
        })

        present(alert, animated: true)
    }
}
