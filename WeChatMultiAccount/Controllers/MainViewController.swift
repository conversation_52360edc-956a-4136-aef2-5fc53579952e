import UIKit

class MainViewController: UIViewController {

    // MARK: - UI Components
    private let tableView = UITableView()
    private let addButton = UIBarButtonItem()
    private let settingsButton = UIBarButtonItem()
    private let emptyStateView = UIView()
    private let emptyStateLabel = UILabel()
    private let emptyStateImageView = UIImageView()

    // MARK: - Properties
    private let accountManager = AccountManager.shared

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupObservers()
        updateEmptyState()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        tableView.reloadData()
        updateEmptyState()
    }

    // MARK: - UI Setup
    private func setupUI() {
        title = "微信多账号管理"
        view.backgroundColor = .systemBackground

        // 设置导航栏
        navigationController?.navigationBar.prefersLargeTitles = true

        // 添加按钮
        addButton.image = UIImage(systemName: "plus.circle.fill")
        addButton.target = self
        addButton.action = #selector(addAccountTapped)

        // 设置按钮
        settingsButton.image = UIImage(systemName: "gearshape.fill")
        settingsButton.target = self
        settingsButton.action = #selector(settingsTapped)

        navigationItem.rightBarButtonItems = [addButton, settingsButton]

        // 设置表格视图
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(AccountTableViewCell.self, forCellReuseIdentifier: "AccountCell")
        tableView.separatorStyle = .singleLine
        tableView.backgroundColor = .systemBackground
        tableView.rowHeight = 80

        // 设置空状态视图
        setupEmptyStateView()

        view.addSubview(tableView)
        view.addSubview(emptyStateView)
    }

    private func setupEmptyStateView() {
        emptyStateView.backgroundColor = .systemBackground
        emptyStateView.isHidden = true

        // 空状态图片
        emptyStateImageView.image = UIImage(systemName: "person.3.fill")
        emptyStateImageView.tintColor = .systemGray3
        emptyStateImageView.contentMode = .scaleAspectFit

        // 空状态文字
        emptyStateLabel.text = "还没有添加任何账号\n点击右上角 + 号添加第一个账号"
        emptyStateLabel.textColor = .secondaryLabel
        emptyStateLabel.textAlignment = .center
        emptyStateLabel.numberOfLines = 0
        emptyStateLabel.font = .systemFont(ofSize: 16)

        emptyStateView.addSubview(emptyStateImageView)
        emptyStateView.addSubview(emptyStateLabel)
    }

    private func setupConstraints() {
        tableView.translatesAutoresizingMaskIntoConstraints = false
        emptyStateView.translatesAutoresizingMaskIntoConstraints = false
        emptyStateImageView.translatesAutoresizingMaskIntoConstraints = false
        emptyStateLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // 表格视图约束
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // 空状态视图约束
            emptyStateView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            emptyStateView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            emptyStateView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            emptyStateView.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // 空状态图片约束
            emptyStateImageView.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            emptyStateImageView.centerYAnchor.constraint(equalTo: emptyStateView.centerYAnchor, constant: -50),
            emptyStateImageView.widthAnchor.constraint(equalToConstant: 80),
            emptyStateImageView.heightAnchor.constraint(equalToConstant: 80),

            // 空状态文字约束
            emptyStateLabel.topAnchor.constraint(equalTo: emptyStateImageView.bottomAnchor, constant: 20),
            emptyStateLabel.leadingAnchor.constraint(equalTo: emptyStateView.leadingAnchor, constant: 40),
            emptyStateLabel.trailingAnchor.constraint(equalTo: emptyStateView.trailingAnchor, constant: -40)
        ])
    }

    private func setupObservers() {
        // 监听账号变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(accountsDidChange),
            name: NSNotification.Name("AccountsDidChange"),
            object: nil
        )
    }

    // MARK: - Actions
    @objc private func addAccountTapped() {
        showAddAccountAlert()
    }

    @objc private func settingsTapped() {
        showSettingsMenu()
    }

    @objc private func accountsDidChange() {
        DispatchQueue.main.async {
            self.tableView.reloadData()
            self.updateEmptyState()
        }
    }

    // MARK: - Private Methods

    private func updateEmptyState() {
        let isEmpty = accountManager.accounts.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
    }

    private func showAddAccountAlert() {
        let alert = UIAlertController(title: "添加新账号", message: "请输入账号信息", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "账号名称（必填）"
            textField.autocapitalizationType = .none
            textField.clearButtonMode = .whileEditing
        }

        alert.addTextField { textField in
            textField.placeholder = "显示昵称（可选）"
            textField.clearButtonMode = .whileEditing
        }

        let addAction = UIAlertAction(title: "添加", style: .default) { [weak self] _ in
            guard let nameField = alert.textFields?[0],
                  let nicknameField = alert.textFields?[1],
                  let name = nameField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
                  !name.isEmpty else {
                self?.showErrorAlert(message: "账号名称不能为空")
                return
            }

            let nickname = nicknameField.text?.trimmingCharacters(in: .whitespacesAndNewlines)
            let finalNickname = nickname?.isEmpty == false ? nickname : nil

            if self?.accountManager.addAccount(name: name, nickname: finalNickname) == true {
                self?.tableView.reloadData()
                self?.updateEmptyState()
                self?.showSuccessMessage("账号添加成功")
            } else {
                self?.showErrorAlert(message: "账号名称已存在，请使用其他名称")
            }
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(addAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func showSettingsMenu() {
        let alert = UIAlertController(title: "设置", message: "选择操作", preferredStyle: .actionSheet)

        // 清除所有Cookie
        let clearCookiesAction = UIAlertAction(title: "清除所有登录状态", style: .destructive) { [weak self] _ in
            self?.confirmClearAllCookies()
        }

        // 关于应用
        let aboutAction = UIAlertAction(title: "关于应用", style: .default) { [weak self] _ in
            self?.showAboutApp()
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(clearCookiesAction)
        alert.addAction(aboutAction)
        alert.addAction(cancelAction)

        // iPad支持
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = settingsButton
        }

        present(alert, animated: true)
    }

    private func confirmClearAllCookies() {
        let alert = UIAlertController(
            title: "清除登录状态",
            message: "确定要清除所有账号的登录状态吗？此操作不可撤销。",
            preferredStyle: .alert
        )

        let clearAction = UIAlertAction(title: "清除", style: .destructive) { [weak self] _ in
            self?.accountManager.clearAllCookies()
            self?.showSuccessMessage("所有登录状态已清除")
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(clearAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func showAboutApp() {
        let alert = UIAlertController(
            title: "微信多账号管理",
            message: "版本 1.0\n\n基于微信网页版的多账号管理工具\n安全、便捷、合规\n\n© 2024 Your Company",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showAccountOptions(for account: WeChatAccount) {
        let alert = UIAlertController(title: account.displayName, message: "选择操作", preferredStyle: .actionSheet)

        // 登录微信
        let loginAction = UIAlertAction(title: "登录微信", style: .default) { [weak self] _ in
            self?.loginToWeChat(account: account)
        }

        // 编辑账号
        let editAction = UIAlertAction(title: "编辑账号", style: .default) { [weak self] _ in
            self?.editAccount(account)
        }

        // 清除登录状态
        let clearCookieAction = UIAlertAction(title: "清除登录状态", style: .destructive) { [weak self] _ in
            self?.accountManager.clearCookies(for: account.id)
            self?.tableView.reloadData()
            self?.showSuccessMessage("登录状态已清除")
        }

        // 删除账号
        let deleteAction = UIAlertAction(title: "删除账号", style: .destructive) { [weak self] _ in
            self?.confirmDeleteAccount(account)
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(loginAction)
        alert.addAction(editAction)
        alert.addAction(clearCookieAction)
        alert.addAction(deleteAction)
        alert.addAction(cancelAction)

        // iPad支持
        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
            popover.permittedArrowDirections = []
        }

        present(alert, animated: true)
    }

    private func loginToWeChat(account: WeChatAccount) {
        // 切换到指定账号
        if accountManager.switchToAccount(id: account.id) {
            // 打开微信Web版
            let webViewController = WebViewController(account: account)
            let navController = UINavigationController(rootViewController: webViewController)
            navController.modalPresentationStyle = .fullScreen
            present(navController, animated: true)
        } else {
            showErrorAlert(message: "账号切换失败")
        }
    }

    private func editAccount(_ account: WeChatAccount) {
        let alert = UIAlertController(title: "编辑账号", message: "修改账号信息", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.text = account.name
            textField.placeholder = "账号名称"
            textField.clearButtonMode = .whileEditing
        }

        alert.addTextField { textField in
            textField.text = account.nickname
            textField.placeholder = "显示昵称（可选）"
            textField.clearButtonMode = .whileEditing
        }

        let saveAction = UIAlertAction(title: "保存", style: .default) { [weak self] _ in
            guard let nameField = alert.textFields?[0],
                  let nicknameField = alert.textFields?[1],
                  let name = nameField.text?.trimmingCharacters(in: .whitespacesAndNewlines),
                  !name.isEmpty else {
                self?.showErrorAlert(message: "账号名称不能为空")
                return
            }

            var updatedAccount = account
            updatedAccount.name = name
            let nickname = nicknameField.text?.trimmingCharacters(in: .whitespacesAndNewlines)
            updatedAccount.nickname = nickname?.isEmpty == false ? nickname : nil

            if self?.accountManager.updateAccount(updatedAccount) == true {
                self?.tableView.reloadData()
                self?.showSuccessMessage("账号信息已更新")
            } else {
                self?.showErrorAlert(message: "账号更新失败")
            }
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(saveAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func confirmDeleteAccount(_ account: WeChatAccount) {
        let alert = UIAlertController(
            title: "删除账号",
            message: "确定要删除账号 \"\(account.displayName)\" 吗？此操作不可撤销。",
            preferredStyle: .alert
        )

        let deleteAction = UIAlertAction(title: "删除", style: .destructive) { [weak self] _ in
            if self?.accountManager.deleteAccount(id: account.id) == true {
                self?.tableView.reloadData()
                self?.updateEmptyState()
                self?.showSuccessMessage("账号已删除")
            } else {
                self?.showErrorAlert(message: "账号删除失败")
            }
        }

        let cancelAction = UIAlertAction(title: "取消", style: .cancel)

        alert.addAction(deleteAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func showErrorAlert(message: String) {
        let alert = UIAlertController(title: "错误", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessMessage(_ message: String) {
        // 创建一个简单的成功提示
        let alert = UIAlertController(title: "成功", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource
extension MainViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return accountManager.accounts.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "AccountCell", for: indexPath) as! AccountTableViewCell
        let account = accountManager.accounts[indexPath.row]
        cell.configure(with: account)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension MainViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let account = accountManager.accounts[indexPath.row]
        showAccountOptions(for: account)
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }
}

// MARK: - 自定义表格单元格
class AccountTableViewCell: UITableViewCell {

    private let avatarImageView = UIImageView()
    private let nameLabel = UILabel()
    private let nicknameLabel = UILabel()
    private let statusLabel = UILabel()
    private let lastLoginLabel = UILabel()
    private let statusIndicator = UIView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        // 头像
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.clipsToBounds = true
        avatarImageView.layer.cornerRadius = 25
        avatarImageView.backgroundColor = .systemGray5
        avatarImageView.image = UIImage(systemName: "person.circle.fill")
        avatarImageView.tintColor = .systemGray3

        // 名称标签
        nameLabel.font = .systemFont(ofSize: 16, weight: .medium)
        nameLabel.textColor = .label

        // 昵称标签
        nicknameLabel.font = .systemFont(ofSize: 14)
        nicknameLabel.textColor = .secondaryLabel

        // 状态标签
        statusLabel.font = .systemFont(ofSize: 12, weight: .medium)
        statusLabel.layer.cornerRadius = 8
        statusLabel.clipsToBounds = true
        statusLabel.textAlignment = .center

        // 状态指示器
        statusIndicator.layer.cornerRadius = 4
        statusIndicator.clipsToBounds = true

        // 最后登录时间
        lastLoginLabel.font = .systemFont(ofSize: 12)
        lastLoginLabel.textColor = .tertiaryLabel

        [avatarImageView, nameLabel, nicknameLabel, statusLabel, lastLoginLabel, statusIndicator].forEach {
            $0.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview($0)
        }

        NSLayoutConstraint.activate([
            avatarImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            avatarImageView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            avatarImageView.widthAnchor.constraint(equalToConstant: 50),
            avatarImageView.heightAnchor.constraint(equalToConstant: 50),

            nameLabel.leadingAnchor.constraint(equalTo: avatarImageView.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            nameLabel.trailingAnchor.constraint(lessThanOrEqualTo: statusLabel.leadingAnchor, constant: -8),

            nicknameLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            nicknameLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            nicknameLabel.trailingAnchor.constraint(lessThanOrEqualTo: statusLabel.leadingAnchor, constant: -8),

            lastLoginLabel.leadingAnchor.constraint(equalTo: nameLabel.leadingAnchor),
            lastLoginLabel.topAnchor.constraint(equalTo: nicknameLabel.bottomAnchor, constant: 2),
            lastLoginLabel.trailingAnchor.constraint(lessThanOrEqualTo: statusLabel.leadingAnchor, constant: -8),

            statusIndicator.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            statusIndicator.centerYAnchor.constraint(equalTo: contentView.centerYAnchor, constant: -10),
            statusIndicator.widthAnchor.constraint(equalToConstant: 8),
            statusIndicator.heightAnchor.constraint(equalToConstant: 8),

            statusLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            statusLabel.topAnchor.constraint(equalTo: statusIndicator.bottomAnchor, constant: 4),
            statusLabel.widthAnchor.constraint(equalToConstant: 60),
            statusLabel.heightAnchor.constraint(equalToConstant: 20)
        ])
    }

    func configure(with account: WeChatAccount) {
        nameLabel.text = account.name
        nicknameLabel.text = account.nickname ?? "未设置昵称"
        lastLoginLabel.text = account.lastLoginText

        // 设置状态
        if account.isActive {
            statusLabel.text = "在线"
            statusLabel.backgroundColor = .systemGreen
            statusLabel.textColor = .white
            statusIndicator.backgroundColor = .systemGreen
        } else {
            statusLabel.text = "离线"
            statusLabel.backgroundColor = .systemGray4
            statusLabel.textColor = .secondaryLabel
            statusIndicator.backgroundColor = .systemGray4
        }

        // 设置头像
        if let avatarData = account.avatarData,
           let avatarImage = UIImage(data: avatarData) {
            avatarImageView.image = avatarImage
        } else {
            avatarImageView.image = UIImage(systemName: "person.circle.fill")
            avatarImageView.tintColor = .systemGray3
        }

        // 设置选中效果
        selectionStyle = .default
        accessoryType = .disclosureIndicator
    }
}
