import Foundation
import UIKit

// MARK: - 账号数据模型
struct WeChatAccount: Codable, Identifiable {
    let id: UUID
    var name: String
    var nickname: String?
    var avatarData: Data?
    var isActive: Bool
    var lastLoginDate: Date?
    var cookieData: Data?
    var createdDate: Date
    
    init(name: String, nickname: String? = nil) {
        self.id = UUID()
        self.name = name
        self.nickname = nickname
        self.isActive = false
        self.lastLoginDate = nil
        self.cookieData = nil
        self.avatarData = nil
        self.createdDate = Date()
    }
    
    var displayName: String {
        return nickname?.isEmpty == false ? nickname! : name
    }
    
    var statusText: String {
        return isActive ? "在线" : "离线"
    }
    
    var lastLoginText: String {
        guard let lastLogin = lastLoginDate else {
            return "从未登录"
        }
        
        let formatter = DateFormatter()
        let now = Date()
        let timeInterval = now.timeIntervalSince(lastLogin)
        
        if timeInterval < 60 {
            return "刚刚登录"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            formatter.dateStyle = .short
            formatter.timeStyle = .short
            return formatter.string(from: lastLogin)
        }
    }
}

// MARK: - 账号管理器
class AccountManager: ObservableObject {
    static let shared = AccountManager()
    
    @Published var accounts: [WeChatAccount] = []
    @Published var currentAccount: WeChatAccount?
    
    private let userDefaults = UserDefaults.standard
    private let accountsKey = "WeChatAccounts"
    private let currentAccountKey = "CurrentWeChatAccount"
    
    private init() {
        loadAccounts()
        print("📊 账号管理器初始化完成，当前账号数量: \(accounts.count)")
    }
    
    // MARK: - 账号管理方法
    
    /// 添加新账号
    func addAccount(name: String, nickname: String? = nil) -> Bool {
        // 检查账号名是否已存在
        if accounts.contains(where: { $0.name == name }) {
            print("⚠️ 账号名已存在: \(name)")
            return false
        }
        
        let newAccount = WeChatAccount(name: name, nickname: nickname)
        accounts.append(newAccount)
        saveAccounts()
        
        print("✅ 新账号添加成功: \(name)")
        return true
    }
    
    /// 删除账号
    func deleteAccount(id: UUID) -> Bool {
        guard let index = accounts.firstIndex(where: { $0.id == id }) else {
            print("⚠️ 未找到要删除的账号")
            return false
        }
        
        let accountName = accounts[index].name
        accounts.remove(at: index)
        
        // 如果删除的是当前账号，清除当前账号
        if currentAccount?.id == id {
            currentAccount = nil
            saveCurrentAccount()
        }
        
        // 清除相关的Cookie数据
        clearCookies(for: id)
        
        saveAccounts()
        print("🗑 账号删除成功: \(accountName)")
        return true
    }
    
    /// 更新账号信息
    func updateAccount(_ account: WeChatAccount) -> Bool {
        guard let index = accounts.firstIndex(where: { $0.id == account.id }) else {
            print("⚠️ 未找到要更新的账号")
            return false
        }
        
        accounts[index] = account
        
        // 如果更新的是当前账号，同时更新当前账号
        if currentAccount?.id == account.id {
            currentAccount = account
            saveCurrentAccount()
        }
        
        saveAccounts()
        print("📝 账号更新成功: \(account.name)")
        return true
    }
    
    /// 切换到指定账号
    func switchToAccount(id: UUID) -> Bool {
        guard let targetAccount = accounts.first(where: { $0.id == id }) else {
            print("⚠️ 未找到目标账号")
            return false
        }
        
        // 更新上一个账号的状态
        if let currentId = currentAccount?.id,
           let currentIndex = accounts.firstIndex(where: { $0.id == currentId }) {
            accounts[currentIndex].isActive = false
        }
        
        // 设置新的当前账号
        if let newIndex = accounts.firstIndex(where: { $0.id == id }) {
            accounts[newIndex].isActive = true
            accounts[newIndex].lastLoginDate = Date()
            currentAccount = accounts[newIndex]
        }
        
        saveAccounts()
        saveCurrentAccount()
        
        print("🔄 账号切换成功: \(targetAccount.name)")
        return true
    }
    
    /// 登出当前账号
    func logoutCurrentAccount() {
        guard let currentId = currentAccount?.id,
              let index = accounts.firstIndex(where: { $0.id == currentId }) else {
            return
        }
        
        accounts[index].isActive = false
        currentAccount = nil
        
        saveAccounts()
        saveCurrentAccount()
        
        print("👋 当前账号已登出")
    }
    
    /// 获取账号数量
    var accountCount: Int {
        return accounts.count
    }
    
    /// 获取活跃账号
    var activeAccounts: [WeChatAccount] {
        return accounts.filter { $0.isActive }
    }
    
    /// 获取最近使用的账号
    var recentAccounts: [WeChatAccount] {
        return accounts
            .filter { $0.lastLoginDate != nil }
            .sorted { $0.lastLoginDate! > $1.lastLoginDate! }
    }
    
    // MARK: - 数据持久化
    
    func saveAllData() {
        saveAccounts()
        saveCurrentAccount()
    }
    
    private func saveAccounts() {
        do {
            let encoded = try JSONEncoder().encode(accounts)
            userDefaults.set(encoded, forKey: accountsKey)
            print("💾 账号数据保存成功")
        } catch {
            print("❌ 账号数据保存失败: \(error)")
        }
    }
    
    private func loadAccounts() {
        guard let data = userDefaults.data(forKey: accountsKey) else {
            print("📂 未找到已保存的账号数据")
            return
        }
        
        do {
            accounts = try JSONDecoder().decode([WeChatAccount].self, from: data)
            loadCurrentAccount()
            print("📂 账号数据加载成功，共 \(accounts.count) 个账号")
        } catch {
            print("❌ 账号数据加载失败: \(error)")
            accounts = []
        }
    }
    
    private func saveCurrentAccount() {
        if let currentAccount = currentAccount {
            do {
                let encoded = try JSONEncoder().encode(currentAccount)
                userDefaults.set(encoded, forKey: currentAccountKey)
            } catch {
                print("❌ 当前账号保存失败: \(error)")
            }
        } else {
            userDefaults.removeObject(forKey: currentAccountKey)
        }
    }
    
    private func loadCurrentAccount() {
        guard let data = userDefaults.data(forKey: currentAccountKey) else {
            return
        }
        
        do {
            let decoded = try JSONDecoder().decode(WeChatAccount.self, from: data)
            // 确保当前账号仍然存在于账号列表中
            if accounts.contains(where: { $0.id == decoded.id }) {
                currentAccount = decoded
            }
        } catch {
            print("❌ 当前账号加载失败: \(error)")
        }
    }
    
    // MARK: - Cookie管理
    
    /// 保存账号的Cookie数据
    func saveCookies(for accountId: UUID, cookies: [HTTPCookie]) {
        guard let index = accounts.firstIndex(where: { $0.id == accountId }) else {
            print("⚠️ 保存Cookie失败：未找到账号")
            return
        }
        
        do {
            let cookieData = try NSKeyedArchiver.archivedData(withRootObject: cookies, requiringSecureCoding: false)
            accounts[index].cookieData = cookieData
            saveAccounts()
            print("🍪 Cookie保存成功，账号: \(accounts[index].name)")
        } catch {
            print("❌ Cookie保存失败: \(error)")
        }
    }
    
    /// 获取账号的Cookie数据
    func getCookies(for accountId: UUID) -> [HTTPCookie]? {
        guard let account = accounts.first(where: { $0.id == accountId }),
              let cookieData = account.cookieData else {
            return nil
        }
        
        do {
            let cookies = try NSKeyedUnarchiver.unarchiveObject(with: cookieData) as? [HTTPCookie]
            print("🍪 Cookie加载成功，账号: \(account.name)")
            return cookies
        } catch {
            print("❌ Cookie加载失败: \(error)")
            return nil
        }
    }
    
    /// 清除账号的Cookie数据
    func clearCookies(for accountId: UUID) {
        guard let index = accounts.firstIndex(where: { $0.id == accountId }) else {
            return
        }
        
        accounts[index].cookieData = nil
        saveAccounts()
        print("🧹 Cookie清除成功，账号: \(accounts[index].name)")
    }
    
    /// 清除所有Cookie数据
    func clearAllCookies() {
        for index in accounts.indices {
            accounts[index].cookieData = nil
        }
        saveAccounts()
        print("🧹 所有Cookie已清除")
    }
}
