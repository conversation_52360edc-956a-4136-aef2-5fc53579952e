#!/bin/bash

echo "🚀 使用Xcode命令行工具创建iOS项目..."

# 创建项目目录
mkdir -p WeChatMultiAccount
cd WeChatMultiAccount

# 使用xcodegen创建项目（如果没有安装，我们手动创建）
echo "📱 创建iOS应用项目结构..."

# 创建项目目录结构
mkdir -p WeChatMultiAccount
mkdir -p WeChatMultiAccount/Controllers
mkdir -p WeChatMultiAccount/Services
mkdir -p WeChatMultiAccount/Models
mkdir -p WeChatMultiAccount/Views
mkdir -p WeChatMultiAccount/Resources
mkdir -p WeChatMultiAccount/Assets.xcassets/AppIcon.appiconset
mkdir -p WeChatMultiAccount/Assets.xcassets/AccentColor.colorset
mkdir -p WeChatMultiAccount/Base.lproj

echo "✅ 项目目录结构创建完成"
echo "📝 接下来需要在Xcode中手动创建项目："
echo "1. 打开Xcode"
echo "2. 选择 'Create a new Xcode project'"
echo "3. 选择 'iOS' -> 'App'"
echo "4. 填写项目信息："
echo "   - Product Name: WeChatMultiAccount"
echo "   - Bundle Identifier: com.yourcompany.WeChatMultiAccount"
echo "   - Language: Swift"
echo "   - Interface: UIKit"
echo "   - Use Core Data: 不勾选"
echo "5. 选择保存位置为: $(pwd)"
echo "6. 然后将我们准备的源代码文件复制到项目中"

echo ""
echo "🔧 或者运行以下命令在Xcode中打开当前目录："
echo "open ."
