#!/bin/bash

echo "🚀 创建iOS微信多账号管理项目..."

# 创建项目目录
mkdir -p WeChatMultiAccount
cd WeChatMultiAccount

# 使用swift package init创建基础结构
swift package init --type executable --name WeChatMultiAccount

# 删除Package.swift，我们要创建iOS项目
rm Package.swift
rm -rf Sources Tests

echo "✅ 基础项目结构创建完成"
echo "📱 现在请按照以下步骤在Xcode中创建项目："
echo ""
echo "1. 打开Xcode"
echo "2. 选择 'Create a new Xcode project'"
echo "3. 选择 'iOS' -> 'App'"
echo "4. 填写项目信息："
echo "   - Product Name: WeChatMultiAccount"
echo "   - Bundle Identifier: com.yourcompany.WeChatMultiAccount"
echo "   - Language: Swift"
echo "   - Interface: UIKit"
echo "   - Use Core Data: 不勾选"
echo "5. 保存位置选择: $(pwd)"
echo ""
echo "项目创建完成后，我将为您提供所有源代码文件。"
