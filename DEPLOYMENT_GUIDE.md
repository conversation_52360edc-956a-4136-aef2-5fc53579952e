# iOS微信多账号管理应用 - 部署指南

## 📋 项目概述

本项目是一个**合规的微信多账号管理工具**，基于微信网页版实现，通过WebView容器技术提供多账号切换功能。

### ✅ 合规性说明
- 使用微信官方网页版，不违反微信服务条款
- 不涉及逆向工程或破解技术
- 符合苹果App Store审核指南
- 采用标准的WebView技术实现

## 🛠 技术架构

### 核心技术栈
- **开发语言**: Swift 5.0+
- **最低系统**: iOS 17.6+
- **架构模式**: MVC + 服务层
- **UI框架**: UIKit + 程序化布局
- **Web技术**: WKWebView + Cookie管理

### 主要功能模块
1. **账号管理服务** (`AccountManager`)
   - 多账号数据存储和管理
   - Cookie自动保存和恢复
   - 账号状态跟踪

2. **主界面控制器** (`MainViewController`)
   - 账号列表展示
   - 添加/编辑/删除账号
   - 账号状态管理

3. **Web容器控制器** (`WebViewController`)
   - 微信网页版加载
   - 导航控制和进度显示
   - 自定义样式注入

## 🔧 开发环境配置

### 必要条件
- macOS 14.0+ 
- Xcode 16.3+
- 有效的苹果开发者账号
- iOS设备或模拟器

### 项目配置步骤

#### 1. 打开项目
```bash
cd /Users/<USER>/dWechat/WeChatMultiAccount
open WeChatMultiAccount.xcodeproj
```

#### 2. 配置Bundle Identifier
在Xcode中修改以下设置：
- Target: WeChatMultiAccount
- Bundle Identifier: `com.yourcompany.WeChatMultiAccount`
- 替换 `yourcompany` 为您的开发者账号标识

#### 3. 配置开发者团队
1. 选择项目 → Signing & Capabilities
2. Team: 选择您的开发者团队
3. Signing Certificate: Automatic

#### 4. 配置应用权限
确保以下权限已正确配置：
- Network Access (默认已启用)
- Keychain Sharing (用于安全存储)

## 📱 构建和测试

### 模拟器测试
```bash
# 构建项目
xcodebuild build -project WeChatMultiAccount.xcodeproj -scheme WeChatMultiAccount -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest'

# 运行测试
xcodebuild test -project WeChatMultiAccount.xcodeproj -scheme WeChatMultiAccount -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest'
```

### 真机测试
1. 连接iOS设备到Mac
2. 在Xcode中选择您的设备
3. 点击运行按钮 (⌘+R)

## 🚀 发布流程

### TestFlight内测发布

#### 1. 创建Archive
```bash
# 清理项目
xcodebuild clean -project WeChatMultiAccount.xcodeproj -scheme WeChatMultiAccount

# 创建Archive
xcodebuild archive \
  -project WeChatMultiAccount.xcodeproj \
  -scheme WeChatMultiAccount \
  -destination 'generic/platform=iOS' \
  -archivePath WeChatMultiAccount.xcarchive
```

#### 2. 导出IPA
```bash
# 导出用于App Store的IPA
xcodebuild -exportArchive \
  -archivePath WeChatMultiAccount.xcarchive \
  -exportPath ./Export \
  -exportOptionsPlist ExportOptions.plist
```

#### 3. 上传到App Store Connect
```bash
# 使用Xcode上传
xcrun altool --upload-app \
  --type ios \
  --file "Export/WeChatMultiAccount.ipa" \
  --username "<EMAIL>" \
  --password "your-app-specific-password"
```

### App Store Connect配置

#### 1. 应用信息设置
- **应用名称**: 微信多账号管理
- **副标题**: 安全便捷的多账号切换工具
- **关键词**: 微信,多账号,管理,切换,工具
- **描述**: 详细说明应用功能和合规性

#### 2. 隐私政策
创建隐私政策页面，说明：
- 不收集用户个人信息
- 本地存储账号管理数据
- 使用微信官方网页版服务

#### 3. 审核信息
- **审核备注**: 说明应用基于微信网页版，合规合法
- **演示账号**: 提供测试用的微信账号（如需要）

## ⚠️ 重要注意事项

### 法律合规
1. **微信服务条款**: 应用基于微信网页版，不违反服务条款
2. **苹果政策**: 符合App Store审核指南
3. **用户协议**: 建议添加用户协议说明使用规范

### 技术限制
1. **功能限制**: 仅支持微信网页版功能
2. **设备限制**: 需要网络连接
3. **系统限制**: iOS 17.6+

### 风险提示
1. **账号安全**: 提醒用户保护账号安全
2. **网络安全**: 建议在安全网络环境下使用
3. **数据备份**: 重要数据建议备份

## 🔒 安全措施

### 数据安全
- 使用iOS Keychain存储敏感信息
- Cookie数据本地加密存储
- 不上传用户数据到第三方服务器

### 网络安全
- 强制HTTPS连接
- 证书验证
- 防止中间人攻击

## 📞 技术支持

### 常见问题
1. **登录失败**: 检查网络连接和微信服务状态
2. **账号丢失**: 检查应用权限和存储空间
3. **功能异常**: 重启应用或清除缓存

### 联系方式
- 技术支持邮箱: <EMAIL>
- 用户反馈: <EMAIL>

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**免责声明**: 本应用仅为技术演示，使用者需自行承担使用风险，开发者不对任何损失负责。
