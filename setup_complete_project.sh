#!/bin/bash

echo "🚀 开始创建完整的iOS微信多账号管理项目..."

# 清理旧项目
rm -rf WeChatMultiAccount

# 创建新的iOS项目
echo "📱 创建iOS项目结构..."

# 使用swift package init创建基础结构，然后转换为iOS项目
mkdir -p WeChatMultiAccount
cd WeChatMultiAccount

# 创建Xcode项目目录结构
mkdir -p WeChatMultiAccount.xcodeproj/project.xcworkspace/xcshareddata
mkdir -p WeChatMultiAccount.xcodeproj/xcuserdata

# 创建应用目录结构
mkdir -p WeChatMultiAccount
mkdir -p WeChatMultiAccount/Controllers
mkdir -p WeChatMultiAccount/Services
mkdir -p WeChatMultiAccount/Models
mkdir -p WeChatMultiAccount/Views
mkdir -p WeChatMultiAccount/Utils
mkdir -p WeChatMultiAccount/Resources
mkdir -p WeChatMultiAccount/Assets.xcassets/AppIcon.appiconset
mkdir -p WeChatMultiAccount/Assets.xcassets/AccentColor.colorset
mkdir -p WeChatMultiAccount/Base.lproj

# 创建测试目录
mkdir -p WeChatMultiAccountTests
mkdir -p WeChatMultiAccountUITests

echo "✅ 项目目录结构创建完成"
echo "📝 接下来将创建所有必要的项目文件..."

cd ..
