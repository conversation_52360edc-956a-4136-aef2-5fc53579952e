// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		0F716C66BE7E6ABEE36CA99F /* AccountManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 115F12654555BB0696D94024 /* AccountManager.swift */; };
		2BCFBB771188EB6430BBD351 /* WebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 236F658E659DDD9A1748D3EF /* WebViewController.swift */; };
		72BDCF68CFF7856571348FF9 /* MainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DCEAB0F7B207D176FD47D29 /* MainViewController.swift */; };
		8D7DB4F33E6ED7C86D37E03E /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4891F524AE64EE93006D7666 /* SceneDelegate.swift */; };
		D0322EB0E8D38E9ADA0DC01A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEF8062B10558728A238C213 /* AppDelegate.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		115F12654555BB0696D94024 /* AccountManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountManager.swift; sourceTree = "<group>"; };
		236F658E659DDD9A1748D3EF /* WebViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebViewController.swift; sourceTree = "<group>"; };
		40F6C976CBD28516627728E2 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		4891F524AE64EE93006D7666 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		8DCEAB0F7B207D176FD47D29 /* MainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewController.swift; sourceTree = "<group>"; };
		A0DF2FA29D8A9824169655D0 /* WeChatMultiAccount.app */ = {isa = PBXFileReference; includeInIndex = 0; lastKnownFileType = wrapper.application; path = WeChatMultiAccount.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DEF8062B10558728A238C213 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXGroup section */
		3DABFB1DA1D2BBB9D8D82ADA /* Services */ = {
			isa = PBXGroup;
			children = (
				115F12654555BB0696D94024 /* AccountManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		6D57B130BE69BA0166749F6F = {
			isa = PBXGroup;
			children = (
				A63D6AB75EE56B30CC1B0973 /* WeChatMultiAccount */,
				704C413ECBB8D5452112FD6F /* Products */,
			);
			sourceTree = "<group>";
		};
		704C413ECBB8D5452112FD6F /* Products */ = {
			isa = PBXGroup;
			children = (
				A0DF2FA29D8A9824169655D0 /* WeChatMultiAccount.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A63D6AB75EE56B30CC1B0973 /* WeChatMultiAccount */ = {
			isa = PBXGroup;
			children = (
				DEF8062B10558728A238C213 /* AppDelegate.swift */,
				40F6C976CBD28516627728E2 /* Info.plist */,
				4891F524AE64EE93006D7666 /* SceneDelegate.swift */,
				************************ /* Controllers */,
				3DABFB1DA1D2BBB9D8D82ADA /* Services */,
			);
			path = WeChatMultiAccount;
			sourceTree = "<group>";
		};
		************************ /* Controllers */ = {
			isa = PBXGroup;
			children = (
				8DCEAB0F7B207D176FD47D29 /* MainViewController.swift */,
				236F658E659DDD9A1748D3EF /* WebViewController.swift */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		************************ /* WeChatMultiAccount */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 56EA76EB1F374CF317EF9735 /* Build configuration list for PBXNativeTarget "WeChatMultiAccount" */;
			buildPhases = (
				1AA88B18997F293E60615F47 /* Sources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = WeChatMultiAccount;
			productName = WeChatMultiAccount;
			productReference = A0DF2FA29D8A9824169655D0 /* WeChatMultiAccount.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DC5BDEDB9C9104C816A2E874 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
				TargetAttributes = {
					************************ = {
						DevelopmentTeam = "";
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = ************************ /* Build configuration list for PBXProject "WeChatMultiAccount" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				"zh-Hans",
			);
			mainGroup = 6D57B130BE69BA0166749F6F;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				************************ /* WeChatMultiAccount */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		1AA88B18997F293E60615F47 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0F716C66BE7E6ABEE36CA99F /* AccountManager.swift in Sources */,
				D0322EB0E8D38E9ADA0DC01A /* AppDelegate.swift in Sources */,
				72BDCF68CFF7856571348FF9 /* MainViewController.swift in Sources */,
				8D7DB4F33E6ED7C86D37E03E /* SceneDelegate.swift in Sources */,
				2BCFBB771188EB6430BBD351 /* WebViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		67E8B7290BE87389F7B116E7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = WeChatMultiAccount/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.WeChatMultiAccount;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		89FC60512EA2E675FAFF8CC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		B31D9A1010F8FDD485D8DC06 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = WeChatMultiAccount/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.yourcompany.WeChatMultiAccount;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		************************ /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		56EA76EB1F374CF317EF9735 /* Build configuration list for PBXNativeTarget "WeChatMultiAccount" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B31D9A1010F8FDD485D8DC06 /* Debug */,
				67E8B7290BE87389F7B116E7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		************************ /* Build configuration list for PBXProject "WeChatMultiAccount" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				89FC60512EA2E675FAFF8CC3 /* Debug */,
				************************ /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = DC5BDEDB9C9104C816A2E874 /* Project object */;
}
