#!/bin/bash

echo "🚀 开始构建iOS微信多账号管理应用..."

# 进入项目目录
cd WeChatMultiAccount

# 检查Xcode项目是否存在
if [ ! -f "WeChatMultiAccount.xcodeproj/project.pbxproj" ]; then
    echo "❌ 错误：找不到Xcode项目文件"
    exit 1
fi

echo "✅ 项目文件检查完成"

# 清理之前的构建
echo "🧹 清理之前的构建..."
xcodebuild clean -project WeChatMultiAccount.xcodeproj -scheme WeChatMultiAccount

# 构建项目
echo "🔨 开始构建项目..."
xcodebuild build -project WeChatMultiAccount.xcodeproj -scheme WeChatMultiAccount -destination 'platform=iOS Simulator,name=iPhone 15,OS=latest'

if [ $? -eq 0 ]; then
    echo "✅ 项目构建成功！"
    
    echo ""
    echo "📱 应用功能说明："
    echo "1. 多账号管理：可以添加、编辑、删除多个微信账号"
    echo "2. 账号切换：快速切换不同的微信账号"
    echo "3. Web容器：基于微信网页版，安全可靠"
    echo "4. Cookie管理：自动保存和恢复登录状态"
    echo "5. 用户友好：现代化的iOS界面设计"
    
    echo ""
    echo "🔧 下一步操作："
    echo "1. 在Xcode中打开项目：open WeChatMultiAccount.xcodeproj"
    echo "2. 配置开发者账号和证书"
    echo "3. 选择目标设备或模拟器"
    echo "4. 点击运行按钮进行测试"
    
else
    echo "❌ 项目构建失败，请检查错误信息"
    exit 1
fi
